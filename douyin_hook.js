
// 抖音req_sign逆向Hook代码
// 在浏览器控制台中运行此代码来拦截关键函数

console.log("🚀 开始Hook抖音签名算法...");

// Hook HMAC相关函数
if (window.crypto && window.crypto.subtle) {
    const originalSign = window.crypto.subtle.sign;
    window.crypto.subtle.sign = function(...args) {
        console.log("🔐 crypto.subtle.sign 被调用:", args);
        return originalSign.apply(this, args);
    };
}

// Hook可能的签名函数
const hookFunction = (obj, funcName) => {
    if (obj && obj[funcName]) {
        const original = obj[funcName];
        obj[funcName] = function(...args) {
            console.log(`🎯 ${funcName} 被调用:`, args);
            const result = original.apply(this, args);
            console.log(`📤 ${funcName} 返回:`, result);
            return result;
        };
    }
};

// 尝试Hook常见的函数名
['signWithHmac', 'sign', 'hmac', 'digest'].forEach(funcName => {
    // 在全局对象中搜索
    for (let key in window) {
        try {
            if (window[key] && typeof window[key] === 'object') {
                hookFunction(window[key], funcName);
            }
        } catch (e) {}
    }
});

// Hook Base64编码
const originalBtoa = window.btoa;
window.btoa = function(str) {
    if (str.includes('req_sign') || str.includes('bd-ticket-guard')) {
        console.log("📋 Base64编码被调用:", str);
    }
    return originalBtoa.call(this, str);
};

// 监听网络请求
const originalFetch = window.fetch;
window.fetch = function(...args) {
    const [url, options] = args;
    if (url.includes('douyin.com') || url.includes('aweme')) {
        console.log("🌐 网络请求:", url, options);
    }
    return originalFetch.apply(this, args);
};

console.log("✅ Hook代码已安装，请执行相关操作触发签名算法");
